/**
 * 编辑器顶部工具栏样式
 */
.editor-top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 48px;
  padding: 0 16px;
  background: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  color: #fff;

  .top-bar-left {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;

    .project-name {
      font-size: 14px;
      font-weight: 500;
      color: #cccccc;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 300px;
    }

    .view-port-button {
      color: #cccccc;
      
      &:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .top-bar-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;

    .ant-btn {
      color: #cccccc;
      
      &:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .top-bar-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;

    .ant-btn-primary {
      background: #0e639c;
      border-color: #0e639c;

      &:hover {
        background: #1177bb;
        border-color: #1177bb;
      }
    }

    .ant-btn:not(.ant-btn-primary) {
      color: #cccccc;
      border-color: #3e3e42;
      background: #3c3c3c;

      &:hover {
        color: #fff;
        border-color: #007acc;
        background: #505050;
      }
    }
  }
}

