/**
 * 简化的编辑器布局组件
 * 用于测试和调试
 */
import React from 'react';
import { Layout, Typography, Card, Space } from 'antd';
import { useAppSelector } from '../../store';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

interface SimpleEditorLayoutProps {
  projectId: string;
  sceneId: string;
}

export const SimpleEditorLayout: React.FC<SimpleEditorLayoutProps> = ({ projectId, sceneId }) => {
  // 安全地获取状态
  const projectState = useAppSelector((state) => {
    try {
      return state?.project || { currentProject: null, currentScene: null };
    } catch (error) {
      console.warn('Project state access error:', error);
      return { currentProject: null, currentScene: null };
    }
  });

  const authState = useAppSelector((state) => {
    try {
      return state?.auth || { user: null };
    } catch (error) {
      console.warn('Auth state access error:', error);
      return { user: null };
    }
  });

  const { currentProject, currentScene } = projectState;
  const { user } = authState;

  return (
    <Layout style={{ height: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
            DL引擎编辑器 - 简化模式
          </Title>
          <div style={{ marginLeft: 'auto' }}>
            <Text>用户: {user?.username || '未知'}</Text>
          </div>
        </div>
      </Header>
      
      <Content style={{ padding: '24px', background: '#f5f5f5' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card title="项目信息" size="small">
            <p><strong>项目ID:</strong> {projectId}</p>
            <p><strong>项目名称:</strong> {currentProject?.name || '加载中...'}</p>
            <p><strong>项目描述:</strong> {currentProject?.description || '无描述'}</p>
          </Card>
          
          <Card title="场景信息" size="small">
            <p><strong>场景ID:</strong> {sceneId}</p>
            <p><strong>场景名称:</strong> {currentScene?.name || '加载中...'}</p>
            <p><strong>场景描述:</strong> {currentScene?.description || '无描述'}</p>
          </Card>
          
          <Card title="编辑器状态" size="small">
            <p><strong>状态:</strong> 简化模式运行中</p>
            <p><strong>说明:</strong> 这是一个简化的编辑器布局，用于测试基本功能。</p>
            <p><strong>下一步:</strong> 如果此页面正常显示，说明基本的React组件和状态管理工作正常。</p>
          </Card>
          
          <Card title="调试信息" size="small">
            <div style={{ fontFamily: 'monospace', fontSize: '12px' }}>
              <p>项目状态: {JSON.stringify(projectState, null, 2)}</p>
              <p>认证状态: {JSON.stringify(authState, null, 2)}</p>
            </div>
          </Card>
        </Space>
      </Content>
    </Layout>
  );
};

export default SimpleEditorLayout;
