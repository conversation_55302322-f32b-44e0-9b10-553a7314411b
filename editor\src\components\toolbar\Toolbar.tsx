/**
 * 工具栏组件
 */
import React from 'react';
import { Dropdown, Button, Space, Divider, Tooltip } from 'antd';
import {
  PlusOutlined,
  FolderOpenOutlined,
  SaveOutlined,
  ExportOutlined,
  ImportOutlined,
  SettingOutlined,
  UndoOutlined,
  RedoOutlined,
  Sc<PERSON>orOutlined,
  <PERSON>pyOutlined,
  SnippetsOutlined,
  DeleteOutlined,
  ArrowsAltOutlined,
  RotateRightOutlined,
  ColumnWidthOutlined,
  BorderOutlined,
  AppstoreOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SelectOutlined,
  EyeOutlined,
  GlobalOutlined,
  BarsOutlined,
  FolderOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  setTransformMode,
  setTransformSpace,
  setSnapMode,
  setShowGrid,
  setShowAxes,
  setIsPlaying,
  TransformMode,
  TransformSpace,
  SnapMode,
  undo,
  redo
} from '../../store/editor/editorSlice';
import { openDialog, DialogType, PanelType } from '../../store/ui/uiSlice';

interface ToolbarProps {
  onOpenPanel?: (panelType: string) => void;
}

const Toolbar: React.FC<ToolbarProps> = ({ onOpenPanel }) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();

  const {
    showGrid,
    showAxes,
    isPlaying
  } = useAppSelector((state) => state.editor);
  
  // 文件菜单
  const fileMenuItems = [
    {
      key: 'new',
      icon: <PlusOutlined />,
      label: t('editor.newProject'),
      onClick: () => dispatch(openDialog({ type: DialogType.NEW_PROJECT, title: t('editor.newProject') }))
    },
    {
      key: 'open',
      icon: <FolderOpenOutlined />,
      label: t('editor.openProject'),
      onClick: () => dispatch(openDialog({ type: DialogType.OPEN_PROJECT, title: t('editor.openProject') }))
    },
    { type: 'divider' as const },
    {
      key: 'save',
      icon: <SaveOutlined />,
      label: t('editor.saveProject')
    },
    {
      key: 'saveAs',
      icon: <SaveOutlined />,
      label: t('editor.saveProjectAs'),
      onClick: () => dispatch(openDialog({ type: DialogType.SAVE_PROJECT_AS, title: t('editor.saveProjectAs') }))
    },
    { type: 'divider' as const },
    {
      key: 'import',
      icon: <ImportOutlined />,
      label: t('editor.importAsset'),
      onClick: () => dispatch(openDialog({ type: DialogType.IMPORT_ASSET, title: t('editor.importAsset') }))
    },
    {
      key: 'export',
      icon: <ExportOutlined />,
      label: t('editor.exportScene'),
      onClick: () => dispatch(openDialog({ type: DialogType.EXPORT_SCENE, title: t('editor.exportScene') }))
    },
    { type: 'divider' as const },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: t('editor.projectSettings'),
      onClick: () => dispatch(openDialog({ type: DialogType.PROJECT_SETTINGS, title: t('editor.projectSettings') }))
    }
  ];
  
  // 编辑菜单
  const editMenuItems = [
    {
      key: 'undo',
      icon: <UndoOutlined />,
      label: t('editor.undo'),
      onClick: () => dispatch(undo())
    },
    {
      key: 'redo',
      icon: <RedoOutlined />,
      label: t('editor.redo'),
      onClick: () => dispatch(redo())
    },
    { type: 'divider' as const },
    {
      key: 'cut',
      icon: <ScissorOutlined />,
      label: t('editor.cut')
    },
    {
      key: 'copy',
      icon: <CopyOutlined />,
      label: t('editor.copy')
    },
    {
      key: 'paste',
      icon: <SnippetsOutlined />,
      label: t('editor.paste')
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: t('editor.delete')
    }
  ];
  
  // 视图菜单
  const viewMenuItems = [
    {
      key: 'grid',
      icon: <BorderOutlined />,
      label: showGrid ? t('editor.hideGrid') : t('editor.showGrid'),
      onClick: () => dispatch(setShowGrid(!showGrid))
    },
    {
      key: 'axes',
      icon: <AppstoreOutlined />,
      label: showAxes ? t('editor.hideAxes') : t('editor.showAxes'),
      onClick: () => dispatch(setShowAxes(!showAxes))
    },
    { type: 'divider' as const },
    {
      key: 'sceneView',
      icon: <EyeOutlined />,
      label: t('editor.sceneView')
    },
    {
      key: 'gameView',
      icon: <PlayCircleOutlined />,
      label: t('editor.gameView')
    }
  ];
  
  // 工具菜单
  const toolsMenuItems = [
    {
      key: 'select',
      icon: <SelectOutlined />,
      label: t('editor.selectTool')
    },
    {
      key: 'translate',
      icon: <ArrowsAltOutlined />,
      label: t('editor.translateTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.TRANSLATE))
    },
    {
      key: 'rotate',
      icon: <RotateRightOutlined />,
      label: t('editor.rotateTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.ROTATE))
    },
    {
      key: 'scale',
      icon: <ColumnWidthOutlined />,
      label: t('editor.scaleTool'),
      onClick: () => dispatch(setTransformMode(TransformMode.SCALE))
    },
    { type: 'divider' as const },
    {
      key: 'transformSpace',
      label: t('editor.transformSpace'),
      children: [
        {
          key: 'local',
          label: t('editor.localSpace'),
          onClick: () => dispatch(setTransformSpace(TransformSpace.LOCAL))
        },
        {
          key: 'world',
          label: t('editor.worldSpace'),
          onClick: () => dispatch(setTransformSpace(TransformSpace.WORLD))
        }
      ]
    },
    {
      key: 'snapMode',
      label: t('editor.snapMode'),
      children: [
        {
          key: 'disabled',
          label: t('editor.snapDisabled'),
          onClick: () => dispatch(setSnapMode(SnapMode.DISABLED))
        },
        {
          key: 'grid',
          label: t('editor.snapToGrid'),
          onClick: () => dispatch(setSnapMode(SnapMode.GRID))
        },
        {
          key: 'vertex',
          label: t('editor.snapToVertex'),
          onClick: () => dispatch(setSnapMode(SnapMode.VERTEX))
        }
      ]
    }
  ];
  
  // 窗口菜单
  const windowMenuItems = [
    {
      key: 'terrain',
      icon: <GlobalOutlined />,
      label: t('editor.terrain.terrainEditor') || '地形编辑器',
      onClick: () => onOpenPanel?.(PanelType.TERRAIN)
    },
    { type: 'divider' as const },
    {
      key: 'hierarchy',
      icon: <BarsOutlined />,
      label: t('editor.hierarchyView') || '层级面板',
      onClick: () => onOpenPanel?.(PanelType.HIERARCHY)
    },
    {
      key: 'assets',
      icon: <FolderOutlined />,
      label: t('editor.assetsView') || '资源面板',
      onClick: () => onOpenPanel?.(PanelType.ASSETS)
    },
    {
      key: 'console',
      icon: <CodeOutlined />,
      label: t('editor.consoleView') || '控制台',
      onClick: () => onOpenPanel?.(PanelType.CONSOLE)
    }
  ];

  // 帮助菜单
  const helpMenuItems = [
    {
      key: 'documentation',
      label: t('editor.documentation')
    },
    {
      key: 'tutorials',
      label: t('editor.tutorials')
    },
    {
      key: 'about',
      label: t('editor.about')
    }
  ];
  
  return (
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '0 16px', height: '100%' }}>
      {/* 左侧:菜单栏 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Space size="small">
          <Dropdown menu={{ items: fileMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.file')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: editMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.edit')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: viewMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.view')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: toolsMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.tools')}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: windowMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.window') || '窗口'}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: helpMenuItems }} placement="bottomLeft">
            <Button type="text" style={{ color: '#fff' }}>
              {t('editor.help')}
            </Button>
          </Dropdown>
        </Space>
      </div>

      {/* 中间:项目名称 */}
      <div style={{
        color: '#fff',
        fontSize: '14px',
        fontWeight: 500,
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <AppstoreOutlined />
        <span>Electronics Demo Project / Electronics Store</span>
      </div>

      {/* 右侧:工具按钮 */}
      <div>
        <Space size="small">
          <Tooltip title={t('editor.undo')}>
            <Button type="text" icon={<UndoOutlined />} style={{ color: '#fff' }} onClick={() => dispatch(undo())} />
          </Tooltip>
          <Tooltip title={t('editor.redo')}>
            <Button type="text" icon={<RedoOutlined />} style={{ color: '#fff' }} onClick={() => dispatch(redo())} />
          </Tooltip>
          <Divider type="vertical" style={{ backgroundColor: '#444', height: 20, margin: '0 8px' }} />
          <Tooltip title={isPlaying ? t('editor.pause') : t('editor.play')}>
            <Button
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              style={{ color: '#fff' }}
              onClick={() => dispatch(setIsPlaying(!isPlaying))}
            />
          </Tooltip>
          <Divider type="vertical" style={{ backgroundColor: '#444', height: 20, margin: '0 8px' }} />
          <Button
            type="primary"
            style={{
              background: '#1890ff',
              borderColor: '#1890ff'
            }}
          >
            Publish
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default Toolbar;
