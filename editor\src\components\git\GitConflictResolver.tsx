/**
 * Git冲突解决工具组件
 * 用于解决Git合并冲突
 */
import React, { useState, useEffect } from 'react';
import { Card, Button, Tabs, Space, Radio, message, Modal } from 'antd';
import type { TabsProps } from 'antd';
import {
  ExclamationCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  MergeCellsOutlined,
  ArrowLeftOutlined,
  ArrowRightOutlined,
  SaveOutlined,
  DiffOutlined,
  FileOutlined,
  EditOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';

import { setShowConflictPanel, setMergeConflicts, selectShowConflictPanel } from '../../store/git/gitSlice';
import './GitConflictResolver.less';

/**
 * Git冲突解决工具组件
 */
const GitConflictResolver: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { mergeConflicts } = useSelector((state: RootState) => state.git);
  const showConflictPanel = useSelector(selectShowConflictPanel);
  
  const [currentConflictIndex, setCurrentConflictIndex] = useState<number>(0);
  const [resolutionChoice, setResolutionChoice] = useState<'ours' | 'theirs' | 'both' | 'custom'>('both');
  const [customContent, setCustomContent] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isConfirmModalVisible, setIsConfirmModalVisible] = useState<boolean>(false);

  // 当前冲突
  const currentConflict = mergeConflicts[currentConflictIndex];

  // 初始化
  useEffect(() => {
    if (currentConflict) {
      setCustomContent(currentConflict.content);
    }
  }, [currentConflictIndex, mergeConflicts]);

  // 处理关闭面板
  const handleClose = () => {
    if (mergeConflicts.length > 0) {
      setIsConfirmModalVisible(true);
    } else {
      dispatch(setShowConflictPanel(false));
    }
  };

  // 处理确认关闭
  const handleConfirmClose = React.useCallback(() => {
    try {
      console.log('GitConflictResolver: 确认关闭冲突面板');
      dispatch(setShowConflictPanel(false));
      setIsConfirmModalVisible(false);
      console.log('GitConflictResolver: 冲突面板已关闭');
    } catch (error) {
      console.error('GitConflictResolver: 关闭冲突面板失败:', error);
      // 强制关闭
      try {
        setIsConfirmModalVisible(false);
      } catch (fallbackError) {
        console.error('GitConflictResolver: 强制关闭确认对话框失败:', fallbackError);
      }
    }
  }, [dispatch]);

  // 处理取消关闭
  const handleCancelClose = React.useCallback(() => {
    try {
      console.log('GitConflictResolver: 取消关闭操作');
      setIsConfirmModalVisible(false);
    } catch (error) {
      console.error('GitConflictResolver: 取消关闭失败:', error);
      // 强制关闭
      setIsConfirmModalVisible(false);
    }
  }, []);

  // 处理上一个冲突
  const handlePrevConflict = () => {
    if (currentConflictIndex > 0) {
      setCurrentConflictIndex(currentConflictIndex - 1);
      setResolutionChoice('both');
      setIsEditing(false);
    }
  };

  // 处理下一个冲突
  const handleNextConflict = () => {
    if (currentConflictIndex < mergeConflicts.length - 1) {
      setCurrentConflictIndex(currentConflictIndex + 1);
      setResolutionChoice('both');
      setIsEditing(false);
    }
  };

  // 处理解决冲突
  const handleResolveConflict = () => {
    if (!currentConflict) return;

    let resolvedContent = '';

    switch (resolutionChoice) {
      case 'ours':
        resolvedContent = currentConflict.ourContent;
        break;
      case 'theirs':
        resolvedContent = currentConflict.theirContent;
        break;
      case 'both':
        resolvedContent = `${currentConflict.ourContent}\n${currentConflict.theirContent}`;
        break;
      case 'custom':
        resolvedContent = customContent;
        break;
    }

    // 这里应该调用gitService的resolveConflict方法
    // gitService.resolveConflict(currentConflict.path, resolvedContent);
    console.log('Resolving conflict with content:', resolvedContent);

    // 更新冲突列表
    const updatedConflicts = [...mergeConflicts];
    updatedConflicts.splice(currentConflictIndex, 1);
    dispatch(setMergeConflicts(updatedConflicts));

    // 如果没有更多冲突，则完成合并
    if (updatedConflicts.length === 0) {
      message.success(t('git.allConflictsResolved') || '');
      dispatch(setShowConflictPanel(false));
    } else {
      // 调整当前索引
      if (currentConflictIndex >= updatedConflicts.length) {
        setCurrentConflictIndex(updatedConflicts.length - 1);
      }
      message.success(t('git.conflictResolved') || '');
    }
  };

  // 处理解决所有冲突
  const handleResolveAllConflicts = () => {
    Modal.confirm({
      title: t('git.resolveAllConfirmTitle') || '',
      content: t('git.resolveAllConfirmContent', { choice: t(`git.${resolutionChoice}`) }) || '',
      okText: t('git.resolveAll') || '',
      cancelText: t('common.cancel') || '',
      onOk: () => {
        // 这里应该调用gitService的resolveAllConflicts方法
        // gitService.resolveAllConflicts(resolutionChoice);

        message.success(t('git.allConflictsResolved') || '');
        dispatch(setMergeConflicts([]));
        dispatch(setShowConflictPanel(false));
      }});
  };

  // 处理编辑自定义内容
  const handleEditCustomContent = () => {
    setIsEditing(true);
    setResolutionChoice('custom');
  };

  // 处理保存自定义内容
  const handleSaveCustomContent = () => {
    setIsEditing(false);
  };

  // 渲染冲突内容
  const renderConflictContent = () => {
    if (!currentConflict) {
      return (
        <div className="git-conflict-empty">
          <p>{t('git.noConflicts') || '没有冲突'}</p>
        </div>
      );
    }

    return (
      <div className="git-conflict-content">
        <div className="git-conflict-file">
          <FileOutlined />
          <span className="git-conflict-file-path">{currentConflict.path}</span>
        </div>

        <Tabs
          defaultActiveKey="comparison"
          items={[
            {
              key: 'comparison',
              label: (
                <span>
                  <DiffOutlined />
                  {t('git.comparison') || ''}
                </span>
              ),
              children: (
                <div className="git-conflict-comparison">
                  <div className="git-conflict-side">
                    <div className="git-conflict-side-header">
                      <h4>{t('git.ourChanges') || ''}</h4>
                    </div>
                    <pre className="git-conflict-code">{currentConflict.ourContent}</pre>
                  </div>
                  <div className="git-conflict-side">
                    <div className="git-conflict-side-header">
                      <h4>{t('git.theirChanges') || ''}</h4>
                    </div>
                    <pre className="git-conflict-code">{currentConflict.theirContent}</pre>
                  </div>
                </div>
              )
            },
            {
              key: 'editor',
              label: (
                <span>
                  <CodeOutlined />
                  {t('git.editor') || ''}
                </span>
              ),
              children: (
                <div className="git-conflict-editor">
                  {isEditing ? (
                    <>
                      <textarea
                        className="git-conflict-textarea"
                        value={customContent}
                        onChange={(e) => setCustomContent(e.target.value)}
                      />
                      <div className="git-conflict-editor-actions">
                        <Button
                          icon={<SaveOutlined />}
                          onClick={handleSaveCustomContent}
                        >
                          {t('git.save') || ''}
                        </Button>
                      </div>
                    </>
                  ) : (
                    <>
                      <pre className="git-conflict-code">{
                        resolutionChoice === 'ours' ? currentConflict.ourContent :
                        resolutionChoice === 'theirs' ? currentConflict.theirContent :
                        resolutionChoice === 'both' ? `${currentConflict.ourContent}\n${currentConflict.theirContent}` :
                        customContent
                      }</pre>
                      <div className="git-conflict-editor-actions">
                        <Button
                          icon={<EditOutlined />}
                          onClick={handleEditCustomContent}
                        >
                          {t('git.edit') || ''}
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              )
            }
          ]}
        />

        <div className="git-conflict-resolution">
          <h4>{t('git.resolveConflict') || ''}</h4>
          <Radio.Group
            value={resolutionChoice}
            onChange={(e) => setResolutionChoice(e.target.value)}
          >
            <Radio value="ours">{t('git.useOurs') || ''}</Radio>
            <Radio value="theirs">{t('git.useTheirs') || ''}</Radio>
            <Radio value="both">{t('git.useBoth') || ''}</Radio>
            <Radio value="custom">{t('git.useCustom') || ''}</Radio>
          </Radio.Group>
        </div>
      </div>
    );
  };

  // 只有在显示冲突面板且有冲突时才渲染组件
  if (!showConflictPanel || mergeConflicts.length === 0) {
    return null;
  }

  return (
    <div className="git-conflict-resolver" style={{
      position: 'fixed',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      zIndex: 1001, // 比ConflictPanel的zIndex高一点
      maxWidth: '90vw',
      maxHeight: '90vh',
      overflow: 'auto'
    }}>
      <Card
        title={
          <Space>
            <ExclamationCircleOutlined style={{ color: '#faad14' }} />
            <span>{t('git.resolveConflicts') || '解决冲突'}</span>
            <span className="git-conflict-count">
              ({currentConflictIndex + 1}/{mergeConflicts.length})
            </span>
          </Space>
        }
        extra={
          <Button
            icon={<CloseOutlined />}
            onClick={handleClose}
          />
        }
        className="git-conflict-card"
      >
        {renderConflictContent()}

        <div className="git-conflict-actions">
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handlePrevConflict}
              disabled={currentConflictIndex === 0 || mergeConflicts.length === 0}
            >
              {t('git.previous') || '上一个'}
            </Button>
            <Button
              icon={<ArrowRightOutlined />}
              onClick={handleNextConflict}
              disabled={currentConflictIndex === mergeConflicts.length - 1 || mergeConflicts.length === 0}
            >
              {t('git.next') || '下一个'}
            </Button>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleResolveConflict}
              disabled={mergeConflicts.length === 0}
            >
              {t('git.resolve') || '解决'}
            </Button>
            <Button
              icon={<MergeCellsOutlined />}
              onClick={handleResolveAllConflicts}
              disabled={mergeConflicts.length === 0}
            >
              {t('git.resolveAll') || '解决全部'}
            </Button>
          </Space>
        </div>
      </Card>

      <Modal
        title={t('git.confirmCloseTitle', '确认关闭')}
        open={isConfirmModalVisible}
        onOk={handleConfirmClose}
        onCancel={handleCancelClose}
        okText={t('git.confirmClose', '确认关闭')}
        cancelText={t('common.cancel', '取消')}
        destroyOnClose={true}
        keyboard={true}
        maskClosable={true}
        closable={true}
        centered={true}
      >
        <p>{t('git.confirmCloseContent', '确定要关闭冲突解决面板吗？未解决的冲突将保留。')}</p>
      </Modal>
    </div>
  );
};

export default GitConflictResolver;
