/**
 * 编辑器左侧工具栏组件
 * 提供常用的编辑工具（选择、移动、旋转、缩放等）
 */
import React, { useState } from 'react';
import { Tooltip } from 'antd';
import {
  SelectOutlined,
  DragOutlined,
  RotateRightOutlined,
  ExpandOutlined,
  BorderOutlined,
  ScissorOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './LeftToolbar.less';

type ToolType = 'select' | 'move' | 'rotate' | 'scale' | 'box' | 'cut' | 'view' | 'settings';

interface LeftToolbarProps {
  onToolChange?: (tool: ToolType) => void;
}

const LeftToolbar: React.FC<LeftToolbarProps> = ({ onToolChange }) => {
  const { t } = useTranslation();
  const [activeTool, setActiveTool] = useState<ToolType>('select');

  const tools = [
    {
      key: 'select' as ToolType,
      icon: <SelectOutlined />,
      title: t('editor.selectTool') || '选择工具',
      shortcut: 'Q'
    },
    {
      key: 'move' as ToolType,
      icon: <DragOutlined />,
      title: t('editor.translateTool') || '移动工具',
      shortcut: 'W'
    },
    {
      key: 'rotate' as ToolType,
      icon: <RotateRightOutlined />,
      title: t('editor.rotateTool') || '旋转工具',
      shortcut: 'E'
    },
    {
      key: 'scale' as ToolType,
      icon: <ExpandOutlined />,
      title: t('editor.scaleTool') || '缩放工具',
      shortcut: 'R'
    },
    {
      key: 'box' as ToolType,
      icon: <BorderOutlined />,
      title: t('editor.boxSelect') || '框选工具',
      shortcut: 'T'
    },
    {
      key: 'cut' as ToolType,
      icon: <ScissorOutlined />,
      title: t('editor.cutTool') || '剪切工具',
      shortcut: 'Y'
    },
    {
      key: 'view' as ToolType,
      icon: <EyeOutlined />,
      title: t('editor.viewTool') || '视图工具',
      shortcut: 'V'
    },
    {
      key: 'settings' as ToolType,
      icon: <SettingOutlined />,
      title: t('editor.toolSettings') || '工具设置',
      shortcut: 'S'
    }
  ];

  const handleToolClick = (tool: ToolType) => {
    setActiveTool(tool);
    onToolChange?.(tool);
  };

  return (
    <div className="left-toolbar">
      <div className="toolbar-tools">
        {tools.map((tool) => (
          <Tooltip
            key={tool.key}
            title={`${tool.title} (${tool.shortcut})`}
            placement="right"
          >
            <div
              className={`tool-button ${activeTool === tool.key ? 'active' : ''}`}
              onClick={() => handleToolClick(tool.key)}
            >
              {tool.icon}
            </div>
          </Tooltip>
        ))}
      </div>
    </div>
  );
};

export default LeftToolbar;

