/**
 * 编辑器顶部工具栏组件
 * 参考Unity/Unreal Engine的顶部工具栏设计
 */
import React from 'react';
import { Button, Space, Dropdown, Menu, Tooltip } from 'antd';
import {
  AppstoreOutlined,
  BarsOutlined,
  FormatPainterOutlined,
  CloudUploadOutlined,
  PlusOutlined,
  CaretDownOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';
import './EditorTopBar.less';

interface EditorTopBarProps {
  onOpenPanel?: (panelType: string) => void;
  onPublish?: () => void;
  onAddEntity?: () => void;
}

const EditorTopBar: React.FC<EditorTopBarProps> = ({
  onOpenPanel,
  onPublish,
  onAddEntity
}) => {
  const { t } = useTranslation();
  const { currentProject } = useSelector((state: RootState) => state.project);

  // 视图端口菜单
  const viewPortMenu = (
    <Menu
      items={[
        {
          key: 'scene',
          label: t('editor.sceneView') || '场景视图',
          icon: <EyeOutlined />
        },
        {
          key: 'game',
          label: t('editor.gameView') || '游戏视图',
          icon: <EyeOutlined />
        }
      ]}
    />
  );

  // 添加实体菜单
  const addEntityMenu = (
    <Menu
      items={[
        {
          key: 'cube',
          label: t('editor.cube') || '立方体'
        },
        {
          key: 'sphere',
          label: t('editor.sphere') || '球体'
        },
        {
          key: 'plane',
          label: t('editor.plane') || '平面'
        },
        {
          key: 'cylinder',
          label: t('editor.cylinder') || '圆柱体'
        },
        { type: 'divider' },
        {
          key: 'light',
          label: t('editor.light') || '光源',
          children: [
            {
              key: 'directionalLight',
              label: t('editor.directionalLight') || '定向光'
            },
            {
              key: 'pointLight',
              label: t('editor.pointLight') || '点光源'
            },
            {
              key: 'spotLight',
              label: t('editor.spotLight') || '聚光灯'
            }
          ]
        },
        {
          key: 'camera',
          label: t('editor.camera') || '相机'
        }
      ]}
      onClick={({ key }) => {
        if (onAddEntity) {
          onAddEntity();
        }
      }}
    />
  );

  return (
    <div className="editor-top-bar">
      {/* 左侧：项目名称和视图选择 */}
      <div className="top-bar-left">
        <div className="project-name">
          {currentProject?.name || 'DL（Digital Learning）引擎编辑器'}
        </div>
        <Dropdown overlay={viewPortMenu} trigger={['click']}>
          <Button type="text" className="view-port-button">
            {t('editor.viewPort') || '视图端口'}
            <CaretDownOutlined />
          </Button>
        </Dropdown>
      </div>

      {/* 中间：快捷面板入口 */}
      <div className="top-bar-center">
        <Space size="small">
          <Tooltip title={t('editor.hierarchy') || '层级'}>
            <Button
              type="text"
              icon={<BarsOutlined />}
              onClick={() => onOpenPanel?.('hierarchy')}
            >
              {t('editor.hierarchy') || '层级'}
            </Button>
          </Tooltip>
          <Tooltip title={t('editor.materialLibrary') || '材质库'}>
            <Button
              type="text"
              icon={<FormatPainterOutlined />}
              onClick={() => onOpenPanel?.('materials')}
            >
              {t('editor.materialLibrary') || '材质库'}
            </Button>
          </Tooltip>
        </Space>
      </div>

      {/* 右侧：操作按钮 */}
      <div className="top-bar-right">
        <Space size="small">
          <Button
            type="primary"
            icon={<CloudUploadOutlined />}
            onClick={onPublish}
          >
            {t('editor.publish') || '发布'}
          </Button>
          <Dropdown overlay={addEntityMenu} trigger={['click']}>
            <Button icon={<PlusOutlined />}>
              {t('editor.addEntity') || '添加实体'}
            </Button>
          </Dropdown>
        </Space>
      </div>
    </div>
  );
};

export default EditorTopBar;

