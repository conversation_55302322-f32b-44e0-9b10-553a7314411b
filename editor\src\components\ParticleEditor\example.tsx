/**
 * 粒子编辑器使用示例
 */
import React, { useState } from 'react';
import { Button, Modal, message } from 'antd';
import ParticleEditor from './index';

interface ParticleSystemData {
  id?: string;
  name: string;
  maxParticles: number;
  emissionRate: number;
  // ... 其他粒子系统属性
}

const ParticleEditorExample: React.FC = () => {
  const [isEditorVisible, setIsEditorVisible] = useState(false);
  const [editingParticleSystemId, setEditingParticleSystemId] = useState<string | undefined>();
  const [particleSystems, setParticleSystems] = useState<ParticleSystemData[]>([]);

  // 创建新粒子系统
  const handleCreateNew = () => {
    setEditingParticleSystemId(undefined);
    setIsEditorVisible(true);
  };

  // 编辑现有粒子系统
  const handleEdit = (id: string) => {
    setEditingParticleSystemId(id);
    setIsEditorVisible(true);
  };

  // 保存粒子系统
  const handleSave = (particleSystemData: ParticleSystemData) => {
    if (editingParticleSystemId) {
      // 更新现有粒子系统
      setParticleSystems(prev => 
        prev.map(ps => 
          ps.id === editingParticleSystemId 
            ? { ...particleSystemData, id: editingParticleSystemId }
            : ps
        )
      );
      message.success('粒子系统已更新');
    } else {
      // 创建新粒子系统
      const newParticleSystem = {
        ...particleSystemData,
        id: `particle-system-${Date.now()}`
      };
      setParticleSystems(prev => [...prev, newParticleSystem]);
      message.success('粒子系统已创建');
    }
    
    setIsEditorVisible(false);
    setEditingParticleSystemId(undefined);
  };

  // 取消编辑
  const handleCancel = () => {
    setIsEditorVisible(false);
    setEditingParticleSystemId(undefined);
  };

  // 删除粒子系统
  const handleDelete = (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个粒子系统吗？',
      onOk: () => {
        setParticleSystems(prev => prev.filter(ps => ps.id !== id));
        message.success('粒子系统已删除');
      }
    });
  };

  return (
    <div style={{ padding: '24px' }}>
      <h1>粒子系统管理</h1>
      
      {/* 操作按钮 */}
      <div style={{ marginBottom: '24px' }}>
        <Button type="primary" onClick={handleCreateNew}>
          创建新粒子系统
        </Button>
      </div>

      {/* 粒子系统列表 */}
      <div style={{ marginBottom: '24px' }}>
        <h2>现有粒子系统</h2>
        {particleSystems.length === 0 ? (
          <p style={{ color: '#999' }}>暂无粒子系统，点击上方按钮创建一个</p>
        ) : (
          <div style={{ display: 'grid', gap: '16px', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))' }}>
            {particleSystems.map(ps => (
              <div 
                key={ps.id} 
                style={{ 
                  border: '1px solid #d9d9d9', 
                  borderRadius: '6px', 
                  padding: '16px',
                  backgroundColor: '#fafafa'
                }}
              >
                <h3 style={{ margin: '0 0 8px 0' }}>{ps.name}</h3>
                <p style={{ margin: '0 0 16px 0', color: '#666' }}>
                  最大粒子数: {ps.maxParticles} | 发射率: {ps.emissionRate}
                </p>
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Button size="small" onClick={() => handleEdit(ps.id!)}>
                    编辑
                  </Button>
                  <Button size="small" danger onClick={() => handleDelete(ps.id!)}>
                    删除
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 粒子编辑器模态框 */}
      <Modal
        title={editingParticleSystemId ? '编辑粒子系统' : '创建粒子系统'}
        open={isEditorVisible}
        onCancel={handleCancel}
        footer={null}
        width="90%"
        style={{ top: 20 }}
        styles={{ body: { height: 'calc(100vh - 200px)', padding: 0 } }}
        destroyOnClose
        keyboard={true}
        maskClosable={true}
        centered={true}
      >
        <ParticleEditor
          particleSystemId={editingParticleSystemId}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </Modal>

      {/* 使用说明 */}
      <div style={{ marginTop: '48px', padding: '24px', backgroundColor: '#f6f8fa', borderRadius: '6px' }}>
        <h2>使用说明</h2>
        <ul>
          <li><strong>创建粒子系统</strong>: 点击"创建新粒子系统"按钮打开编辑器</li>
          <li><strong>编辑粒子系统</strong>: 在列表中点击"编辑"按钮修改现有粒子系统</li>
          <li><strong>实时预览</strong>: 在编辑器中调整参数时可以实时看到效果</li>
          <li><strong>预设效果</strong>: 使用火焰、烟雾、雪花等预设快速开始</li>
          <li><strong>参数调节</strong>: 通过表单控制发射器、外观、运动等各种参数</li>
          <li><strong>保存和取消</strong>: 编辑完成后点击保存或取消按钮</li>
        </ul>
        
        <h3>技术特性</h3>
        <ul>
          <li>基于 DL Engine 底层引擎</li>
          <li>支持实时参数调节和预览</li>
          <li>响应式设计，适配不同屏幕尺寸</li>
          <li>完整的表单验证和错误处理</li>
          <li>内存管理和资源自动清理</li>
        </ul>
      </div>
    </div>
  );
};

export default ParticleEditorExample;
