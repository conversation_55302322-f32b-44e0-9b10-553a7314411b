# 国际化 (i18n) 故障排除指南

## 概述

本文档提供了关于编辑器国际化功能的故障排除指南，帮助开发者快速定位和解决翻译相关的问题。

## 目录

1. [常见问题](#常见问题)
2. [诊断工具](#诊断工具)
3. [配置说明](#配置说明)
4. [最佳实践](#最佳实践)
5. [调试技巧](#调试技巧)

## 常见问题

### 问题 1: 页面显示翻译键而不是翻译文本

**症状**: 页面显示 `loginTitle` 或 `auth.loginTitle` 而不是 "用户登录"

**可能原因**:
1. 命名空间使用不正确
2. 翻译键不存在
3. i18n 未正确初始化

**解决方案**:

#### 方案 A: 检查命名空间使用

```typescript
// ❌ 错误用法
const { t } = useTranslation();
{t('auth.loginTitle')}  // 会查找 translation:auth.loginTitle

// ✅ 正确用法 1: 使用命名空间
const { t } = useTranslation('auth');
{t('loginTitle')}  // 会查找 auth:loginTitle

// ✅ 正确用法 2: 指定命名空间
const { t } = useTranslation();
{t('loginTitle', { ns: 'auth' })}  // 会查找 auth:loginTitle
```

#### 方案 B: 验证翻译键存在

在浏览器控制台运行:
```javascript
// 检查翻译键是否存在
i18n.t('loginTitle', { ns: 'auth' })

// 查看所有可用的翻译键
i18n.store.data['zh-CN'].auth
```

#### 方案 C: 检查 i18n 初始化

在浏览器控制台运行:
```javascript
// 检查是否已初始化
i18n.isInitialized

// 查看当前语言
i18n.language

// 查看可用资源
Object.keys(i18n.store.data)
```

### 问题 2: 翻译在某些组件中不工作

**症状**: 某些组件显示翻译，某些组件不显示

**可能原因**:
1. 组件在 i18n 初始化前渲染
2. 组件没有正确使用 `useTranslation` hook

**解决方案**:

```typescript
// ✅ 正确使用 useTranslation
import { useTranslation } from 'react-i18next';

const MyComponent: React.FC = () => {
  const { t } = useTranslation('auth');
  
  return <div>{t('loginTitle')}</div>;
};

// ❌ 错误：在组件外部调用
const { t } = useTranslation('auth');  // 不要这样做！

const MyComponent: React.FC = () => {
  return <div>{t('loginTitle')}</div>;
};
```

### 问题 3: 语言切换不生效

**症状**: 调用 `i18n.changeLanguage()` 后界面没有更新

**可能原因**:
1. 组件没有订阅语言变化
2. 使用了错误的语言代码

**解决方案**:

```typescript
// ✅ 正确的语言切换
import { useTranslation } from 'react-i18next';

const LanguageSwitcher: React.FC = () => {
  const { i18n } = useTranslation();
  
  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);  // 会自动触发重新渲染
  };
  
  return (
    <div>
      <button onClick={() => changeLanguage('zh-CN')}>中文</button>
      <button onClick={() => changeLanguage('en-US')}>English</button>
    </div>
  );
};
```

## 诊断工具

### 使用内置诊断工具

编辑器提供了内置的 i18n 诊断工具，可以帮助快速定位问题。

#### 在浏览器控制台使用

```javascript
// 导入诊断工具
import { I18nDiagnostics } from './utils/i18nDiagnostics';

// 运行完整诊断
I18nDiagnostics.printDiagnosticReport();

// 测试特定翻译键
I18nDiagnostics.testTranslation('loginTitle', 'auth');

// 获取所有翻译键
const allKeys = I18nDiagnostics.getAllTranslationKeys('zh-CN');
console.log(allKeys);
```

#### 自动诊断

在开发环境下，诊断工具会在应用启动时自动运行，并在控制台输出诊断报告。

### 诊断报告解读

```
🔍 i18n Diagnostic Report
  ✅ 1. i18n is initialized
    Details: { isInitialized: true, language: 'zh-CN' }
  
  ✅ 2. Language is correctly set to zh-CN
    Details: { currentLanguage: 'zh-CN', fallbackLanguage: 'zh-CN' }
  
  ✅ 3. All expected namespaces are configured
    Details: { configuredNamespaces: ['translation', 'auth'], ... }
  
  ✅ 4. All language resources are loaded
    Details: { loadedLanguages: ['zh-CN', 'en-US'], ... }
  
  ✅ 5. All critical translation keys are available
    Details: { checkedKeys: 6, foundKeys: [...] }

📊 Summary: 5 passed, 0 warnings, 0 errors
```

## 配置说明

### i18n 配置文件结构

```
editor/src/
├── i18n.ts                          # i18n 主配置文件
├── i18n/
│   └── locales/
│       ├── zh-CN.json              # 中文翻译
│       └── en-US.json              # 英文翻译
└── locales/
    ├── zh-CN/
    │   ├── achievements.json       # 成就系统翻译
    │   ├── tutorials.json          # 教程系统翻译
    │   └── help.json              # 帮助系统翻译
    └── en/
        ├── achievements.json
        ├── tutorials.json
        └── help.json
```

### 命名空间配置

当前配置的命名空间:

1. **translation** (默认命名空间)
   - 包含通用翻译
   - 包含 achievements、tutorials、help 子模块

2. **auth** (认证命名空间)
   - 包含登录、注册相关翻译

### 添加新的翻译键

#### 步骤 1: 在 JSON 文件中添加翻译

**zh-CN.json**:
```json
{
  "auth": {
    "newKey": "新的翻译文本"
  }
}
```

**en-US.json**:
```json
{
  "auth": {
    "newKey": "New translation text"
  }
}
```

#### 步骤 2: 在组件中使用

```typescript
const { t } = useTranslation('auth');
const text = t('newKey');
```

## 最佳实践

### 1. 始终使用命名空间

```typescript
// ✅ 推荐：明确指定命名空间
const { t } = useTranslation('auth');

// ⚠️ 不推荐：使用默认命名空间可能导致混淆
const { t } = useTranslation();
```

### 2. 提供回退文本

```typescript
// ✅ 提供回退文本，防止翻译缺失时显示键名
<Input placeholder={t('emailPlaceholder') || '请输入邮箱'} />

// ❌ 不提供回退，可能显示 'emailPlaceholder'
<Input placeholder={t('emailPlaceholder')} />
```

### 3. 使用类型安全的翻译键

```typescript
// 创建类型定义
type AuthTranslationKey = 
  | 'loginTitle'
  | 'registerTitle'
  | 'emailRequired'
  // ... 其他键

// 使用类型约束
const getAuthTranslation = (key: AuthTranslationKey) => {
  return t(key, { ns: 'auth' });
};
```

### 4. 避免在循环中创建翻译函数

```typescript
// ❌ 错误：在循环中重复调用 useTranslation
items.map(item => {
  const { t } = useTranslation();  // 不要这样做！
  return <div>{t(item.key)}</div>;
});

// ✅ 正确：在组件顶层调用一次
const { t } = useTranslation();
items.map(item => {
  return <div>{t(item.key)}</div>;
});
```

## 调试技巧

### 1. 启用调试模式

在 `i18n.ts` 中设置:
```typescript
i18n.init({
  debug: true,  // 启用调试日志
  // ... 其他配置
});
```

### 2. 监听 i18n 事件

```typescript
// 监听语言变化
i18n.on('languageChanged', (lng) => {
  console.log('Language changed to:', lng);
});

// 监听资源加载失败
i18n.on('failedLoading', (lng, ns, msg) => {
  console.error('Failed to load:', lng, ns, msg);
});

// 监听缺失的翻译键
i18n.on('missingKey', (lngs, namespace, key, res) => {
  console.warn('Missing key:', namespace, key);
});
```

### 3. 检查翻译资源

```javascript
// 查看特定语言的所有翻译
console.log(i18n.store.data['zh-CN']);

// 查看特定命名空间的翻译
console.log(i18n.store.data['zh-CN'].auth);

// 查看所有已加载的语言
console.log(Object.keys(i18n.store.data));
```

### 4. 测试翻译插值

```typescript
// 测试带参数的翻译
const translation = t('welcome', { name: '张三' });
console.log(translation);  // 应该输出: "欢迎，张三"
```

## 常见错误代码

### ERR_I18N_NOT_INITIALIZED

**错误**: i18n 未初始化就被使用

**解决**: 确保在 `main.tsx` 中等待 i18n 初始化完成后再渲染应用

### ERR_NAMESPACE_NOT_FOUND

**错误**: 使用了未配置的命名空间

**解决**: 在 `i18n.ts` 的 `ns` 配置中添加命名空间

### ERR_TRANSLATION_KEY_NOT_FOUND

**错误**: 翻译键不存在

**解决**: 在对应的 JSON 文件中添加翻译键

## 获取帮助

如果以上方法都无法解决问题，请:

1. 运行完整的诊断报告并保存输出
2. 检查浏览器控制台的错误信息
3. 提供复现步骤和相关代码
4. 联系开发团队寻求支持

## 相关资源

- [react-i18next 官方文档](https://react.i18next.com/)
- [i18next 官方文档](https://www.i18next.com/)
- [项目 i18n 配置文件](../src/i18n.ts)
- [诊断工具源码](../src/utils/i18nDiagnostics.ts)

