/**
 * 编辑器主布局组件
 * 使用rc-dock库实现可停靠的面板布局
 */
import React, { useState, useCallback } from 'react';
import { Layout, Menu, Button, Dropdown, Modal } from 'antd';
import type { MenuProps } from 'antd';
import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  AppstoreOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  BookOutlined,
  VideoCameraOutlined,
  UserOutlined,
  GlobalOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { DockLayout } from 'rc-dock';
import 'rc-dock/dist/rc-dock.css';
import { useTranslation } from 'react-i18next';

import ScenePanel from './panels/ScenePanel';
import HierarchyPanel from './panels/HierarchyPanel';
import InspectorPanel from './panels/InspectorPanel';
import AssetsPanel from './panels/AssetsPanel';
import ConsolePanel from './panels/ConsolePanel';
import HelpPanel from './panels/HelpPanel';
import TutorialPanel from './tutorials/TutorialPanel';

import './MainLayout.less';

const { Header, Sider, Content } = Layout;

interface MainLayoutProps {
  // 可以添加属性
}

const MainLayout: React.FC<MainLayoutProps> = () => {
  const { t, i18n } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);
  const [helpVisible, setHelpVisible] = useState(false);
  const [tutorialVisible, setTutorialVisible] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language);
  
  // 初始化停靠布局配置
  const defaultLayout = {
    dockbox: {
      mode: 'horizontal' as const,
      children: [
        {
          mode: 'vertical' as const,
          size: 200,
          children: [
            {
              tabs: [
                { id: 'hierarchy', title: t('panels.hierarchy'), content: <HierarchyPanel /> }
              ]
            },
            {
              tabs: [
                { id: 'assets', title: t('panels.assets'), content: <AssetsPanel /> }
              ]
            }
          ]
        },
        {
          mode: 'vertical' as const,
          size: 600,
          children: [
            {
              size: 400,
              tabs: [
                { id: 'scene', title: t('panels.scene'), content: <ScenePanel /> }
              ]
            },
            {
              size: 200,
              tabs: [
                { id: 'console', title: t('panels.console'), content: <ConsolePanel /> }
              ]
            }
          ]
        },
        {
          mode: 'vertical' as const,
          size: 300,
          children: [
            {
              tabs: [
                { id: 'inspector', title: t('panels.inspector'), content: <InspectorPanel /> }
              ]
            }
          ]
        }
      ]
    }
  };

  const [layout] = useState(defaultLayout);
  
  // 切换侧边栏折叠状态
  const toggleCollapsed = useCallback(() => {
    try {
      setCollapsed(prev => !prev);
    } catch (error) {
      console.error('切换侧边栏失败:', error);
    }
  }, []);

  // 切换帮助面板可见性
  const toggleHelp = useCallback(() => {
    try {
      setHelpVisible(prev => !prev);
    } catch (error) {
      console.error('切换帮助面板失败:', error);
    }
  }, []);

  // 切换教程面板可见性
  const toggleTutorial = useCallback(() => {
    try {
      setTutorialVisible(prev => !prev);
    } catch (error) {
      console.error('切换教程面板失败:', error);
    }
  }, []);
  
  // 切换语言
  const changeLanguage = useCallback((lang: string) => {
    try {
      i18n.changeLanguage(lang);
      setCurrentLanguage(lang);
    } catch (error) {
      console.error('切换语言失败:', error);
    }
  }, [i18n]);
  
  // 语言菜单
  const languageMenuItems: MenuProps['items'] = [
    {
      key: 'zh-CN',
      label: '中文',
      onClick: () => changeLanguage('zh-CN')
    },
    {
      key: 'en',
      label: 'English',
      onClick: () => changeLanguage('en')
    }
  ];

  // 帮助菜单
  const helpMenuItems: MenuProps['items'] = [
    {
      key: 'help',
      label: (
        <>
          <QuestionCircleOutlined /> {t('menu.help')}
        </>
      ),
      onClick: toggleHelp
    },
    {
      key: 'tutorials',
      label: (
        <>
          <VideoCameraOutlined /> {t('menu.tutorials')}
        </>
      ),
      onClick: toggleTutorial
    },
    {
      key: 'documentation',
      label: (
        <>
          <BookOutlined /> {t('menu.documentation')}
        </>
      )
    },
    {
      type: 'divider'
    },
    {
      key: 'about',
      label: (
        <>
          <InfoCircleOutlined /> {t('menu.about')}
        </>
      )
    }
  ];
  
  return (
    <Layout className="main-layout">
      <Header className="header">
        <div className="logo">IR Editor</div>
        <Menu theme="dark" mode="horizontal" defaultSelectedKeys={['1']}>
          <Menu.Item key="1">{t('menu.file')}</Menu.Item>
          <Menu.Item key="2">{t('menu.edit')}</Menu.Item>
          <Menu.Item key="3">{t('menu.view')}</Menu.Item>
          <Menu.Item key="4">{t('menu.window')}</Menu.Item>
        </Menu>
        <div className="header-right">
          <Dropdown menu={{ items: languageMenuItems }} placement="bottomRight">
            <Button type="text" icon={<GlobalOutlined />} style={{ color: 'white' }}>
              {currentLanguage === 'zh-CN' ? '中文' : 'English'}
            </Button>
          </Dropdown>
          <Dropdown menu={{ items: helpMenuItems }} placement="bottomRight">
            <Button type="text" icon={<QuestionCircleOutlined />} style={{ color: 'white' }} />
          </Dropdown>
          <Button type="text" icon={<UserOutlined />} style={{ color: 'white' }} />
        </div>
      </Header>
      <Layout>
        <Sider trigger={null} collapsible collapsed={collapsed} width={200}>
          <Button 
            type="text" 
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={toggleCollapsed}
            style={{ margin: '16px', color: 'white' }}
          />
          <Menu theme="dark" mode="inline" defaultSelectedKeys={['1']}>
            <Menu.Item key="1" icon={<AppstoreOutlined />}>
              {t('menu.project')}
            </Menu.Item>
            <Menu.Item key="2" icon={<SettingOutlined />}>
              {t('menu.settings')}
            </Menu.Item>
          </Menu>
        </Sider>
        <Content className="content">
          <DockLayout
            defaultLayout={layout}
            style={{ position: 'absolute', left: 0, top: 0, right: 0, bottom: 0 }}
          />
        </Content>
      </Layout>
      
      {/* 帮助面板模态框 */}
      <Modal
        title={t('help.title')}
        open={helpVisible}
        onCancel={toggleHelp}
        footer={null}
        width={800}
        styles={{ body: { height: '600px', padding: 0, overflow: 'hidden' } }}
        destroyOnClose
        keyboard={true}
        maskClosable={true}
        centered={true}
      >
        <HelpPanel />
      </Modal>

      {/* 教程面板模态框 */}
      <Modal
        title={t('tutorials.title')}
        open={tutorialVisible}
        onCancel={toggleTutorial}
        footer={null}
        width={800}
        styles={{ body: { height: '600px', padding: 0, overflow: 'hidden' } }}
        destroyOnClose
        keyboard={true}
        maskClosable={true}
        centered={true}
      >
        <TutorialPanel onClose={toggleTutorial} />
      </Modal>
    </Layout>
  );
};

export default MainLayout;
