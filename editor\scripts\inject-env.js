#!/usr/bin/env node

/**
 * 环境变量注入脚本
 * 在构建时将环境变量注入到HTML文件中
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath, pathToFileURL } from 'url';

// 获取当前文件的目录路径（ES模块中的__dirname替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 需要注入的环境变量
const ENV_VARS = [
  'NODE_ENV',
  'REACT_APP_API_URL',
  'REACT_APP_COLLABORATION_SERVER_URL',
  'REACT_APP_MINIO_ENDPOINT',
  'REACT_APP_ENVIRONMENT',
  'REACT_APP_VERSION',
  'REACT_APP_ENABLE_DEBUG',
  'REACT_APP_LOG_LEVEL',
  'REACT_APP_ENABLE_ANALYTICS',
  'REACT_APP_ENABLE_MOCK_DATA',
  'LANG',
  'LANGUAGE',
  'LC_ALL',
  'TZ',
];

/**
 * 获取环境变量
 */
function getEnvironmentVariables() {
  const env = {};
  
  ENV_VARS.forEach(varName => {
    const value = process.env[varName];
    if (value !== undefined) {
      env[varName] = value;
    }
  });
  
  return env;
}

/**
 * 生成环境变量脚本
 */
function generateEnvScript(env) {
  // 确保语言环境变量存在
  const envWithDefaults = {
    ...env,
    LANG: env.LANG || 'zh_CN.UTF-8',
    LANGUAGE: env.LANGUAGE || 'zh_CN:zh',
    LC_ALL: env.LC_ALL || 'zh_CN.UTF-8',
    TZ: env.TZ || 'Asia/Shanghai',
    REACT_APP_FORCE_LANGUAGE: 'zh-CN'
  };

  return `
<script>
  window.__ENV__ = ${JSON.stringify(envWithDefaults, null, 2)};
  // 强制设置语言环境
  window.__FORCE_LANGUAGE__ = 'zh-CN';
  console.log('🌐 环境变量已注入，强制语言:', window.__FORCE_LANGUAGE__);
</script>`;
}

/**
 * 注入环境变量到HTML文件
 */
function injectEnvToHtml(htmlPath, env) {
  try {
    // 读取HTML文件
    let htmlContent = fs.readFileSync(htmlPath, 'utf8');

    // 检查是否已经存在环境变量脚本
    const envScriptPattern = /<script>\s*window\.__ENV__\s*=[\s\S]*?<\/script>/g;

    // 移除已存在的环境变量脚本
    htmlContent = htmlContent.replace(envScriptPattern, '');

    // 生成新的环境变量脚本
    const envScript = generateEnvScript(env);

    // 查找head标签并注入脚本
    const headCloseTag = '</head>';
    const headIndex = htmlContent.indexOf(headCloseTag);

    if (headIndex !== -1) {
      htmlContent = htmlContent.slice(0, headIndex) +
                   envScript + '\n' +
                   htmlContent.slice(headIndex);
    } else {
      console.warn('未找到</head>标签，将脚本添加到文件开头');
      htmlContent = envScript + '\n' + htmlContent;
    }

    // 写回文件
    fs.writeFileSync(htmlPath, htmlContent, 'utf8');

    console.log('✅ 环境变量已成功注入到', htmlPath);
    console.log('📋 注入的环境变量:', Object.keys(env));

  } catch (error) {
    console.error('❌ 注入环境变量失败:', error.message);
    process.exit(1);
  }
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  
  // 默认HTML文件路径
  let htmlPath = path.join(__dirname, '../dist/index.html');
  
  // 如果提供了参数，使用参数作为HTML文件路径
  if (args.length > 0) {
    htmlPath = path.resolve(args[0]);
  }
  
  // 检查HTML文件是否存在
  if (!fs.existsSync(htmlPath)) {
    console.error('❌ HTML文件不存在:', htmlPath);
    console.log('💡 请确保已运行构建命令: npm run build');
    process.exit(1);
  }
  
  // 获取环境变量
  const env = getEnvironmentVariables();
  
  if (Object.keys(env).length === 0) {
    console.warn('⚠️  未找到任何环境变量');
    console.log('💡 支持的环境变量:', ENV_VARS.join(', '));
  }
  
  // 注入环境变量
  injectEnvToHtml(htmlPath, env);
}

// 如果直接运行此脚本（ES模块中的检查方式）
if (import.meta.url === pathToFileURL(process.argv[1]).href) {
  main();
}



// ES模块导出
export {
  getEnvironmentVariables,
  generateEnvScript,
  injectEnvToHtml,
  ENV_VARS
};
