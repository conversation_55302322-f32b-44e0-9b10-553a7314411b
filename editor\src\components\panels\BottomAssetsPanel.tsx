/**
 * 底部资源面板组件
 * 显示场景、文件、资源、可视化脚本等
 */
import React, { useState } from 'react';
import { Tabs, Breadcrumb, Input, Button, Card, Row, Col, Empty } from 'antd';
import {
  AppstoreOutlined,
  FolderOutlined,
  FileOutlined,
  CodeOutlined,
  SearchOutlined,
  UploadOutlined,
  PictureOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './BottomAssetsPanel.less';

const { TabPane } = Tabs;
const { Search } = Input;

interface Asset {
  id: string;
  name: string;
  type: string;
  thumbnail?: string;
}

const BottomAssetsPanel: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('assets');
  const [searchText, setSearchText] = useState('');
  const [currentPath, setCurrentPath] = useState(['Models', 'Props', 'Misc']);

  // 示例资源数据
  const sampleAssets: Asset[] = [
    { id: '1', name: 'Pencil Box', type: 'FBX & GLB', thumbnail: '' },
    { id: '2', name: 'Headphones', type: 'FBX & GLB', thumbnail: '' },
    { id: '3', name: 'Synthesizer Keyboard', type: 'FBX & GLB', thumbnail: '' },
    { id: '4', name: 'Studio Headphones', type: 'FBX & GLB', thumbnail: '' }
  ];

  const handleSearch = (value: string) => {
    setSearchText(value);
    console.log('搜索资源:', value);
  };

  const handleUpload = () => {
    console.log('上传资源');
  };

  const handleAssetClick = (asset: Asset) => {
    console.log('选中资源:', asset);
  };

  const renderAssetGrid = () => {
    return (
      <div className="asset-grid-container">
        <Row gutter={[16, 16]}>
          {sampleAssets.map((asset) => (
            <Col key={asset.id} span={6}>
              <Card
                hoverable
                className="asset-card"
                onClick={() => handleAssetClick(asset)}
                cover={
                  <div className="asset-thumbnail">
                    <PictureOutlined style={{ fontSize: 48, color: '#999' }} />
                  </div>
                }
              >
                <Card.Meta
                  title={asset.name}
                  description={asset.type}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  return (
    <div className="bottom-assets-panel">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        className="assets-tabs"
        tabBarExtraContent={
          <div className="tab-bar-actions">
            <Search
              placeholder={t('editor.searchAssets') || '搜索资源'}
              onSearch={handleSearch}
              style={{ width: 200, marginRight: 8 }}
              prefix={<SearchOutlined />}
            />
            <Button
              icon={<UploadOutlined />}
              onClick={handleUpload}
            >
              {t('editor.uploadAssets') || '上传资源'}
            </Button>
          </div>
        }
      >
        <TabPane
          tab={
            <span>
              <AppstoreOutlined />
              {t('editor.scenes') || '场景'}
            </span>
          }
          key="scenes"
        >
          <div className="panel-content">
            <Empty description={t('editor.noScenes') || '暂无场景'} />
          </div>
        </TabPane>

        <TabPane
          tab={
            <span>
              <FolderOutlined />
              {t('editor.files') || '文件'}
            </span>
          }
          key="files"
        >
          <div className="panel-content">
            <Empty description={t('editor.noFiles') || '暂无文件'} />
          </div>
        </TabPane>

        <TabPane
          tab={
            <span>
              <FileOutlined />
              {t('editor.assets') || '资源'}
            </span>
          }
          key="assets"
        >
          <div className="panel-content">
            {/* 面包屑导航 */}
            <div className="breadcrumb-container">
              <Breadcrumb>
                {currentPath.map((path, index) => (
                  <Breadcrumb.Item key={index}>{path}</Breadcrumb.Item>
                ))}
              </Breadcrumb>
            </div>

            {/* 资源网格 */}
            {renderAssetGrid()}
          </div>
        </TabPane>

        <TabPane
          tab={
            <span>
              <CodeOutlined />
              {t('editor.visualScripting') || '可视化脚本'}
            </span>
          }
          key="visualScripting"
        >
          <div className="panel-content">
            <Empty description={t('editor.noScripts') || '暂无脚本'} />
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default BottomAssetsPanel;

