/**
 * 底部资源面板样式
 */
.bottom-assets-panel {
  height: 100%;
  background: #252526;
  border-top: 1px solid #3e3e42;
  display: flex;
  flex-direction: column;

  .assets-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .ant-tabs-nav {
      background: #2d2d30;
      margin: 0;
      padding: 0 16px;

      .ant-tabs-tab {
        color: #cccccc;
        padding: 12px 16px;

        &:hover {
          color: #fff;
        }

        &.ant-tabs-tab-active {
          color: #fff;

          .ant-tabs-tab-btn {
            color: #fff;
          }
        }

        .anticon {
          margin-right: 8px;
        }
      }

      .ant-tabs-ink-bar {
        background: #007acc;
      }
    }

    .ant-tabs-content-holder {
      flex: 1;
      overflow: hidden;
    }

    .ant-tabs-content {
      height: 100%;
    }

    .ant-tabs-tabpane {
      height: 100%;
    }
  }

  .tab-bar-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;

    .ant-input-search {
      .ant-input {
        background: #3c3c3c;
        border-color: #3e3e42;
        color: #cccccc;

        &::placeholder {
          color: #858585;
        }

        &:hover,
        &:focus {
          border-color: #007acc;
        }
      }

      .ant-input-search-button {
        background: #0e639c;
        border-color: #0e639c;

        &:hover {
          background: #1177bb;
          border-color: #1177bb;
        }
      }
    }

    .ant-btn {
      background: #0e639c;
      border-color: #0e639c;
      color: #fff;

      &:hover {
        background: #1177bb;
        border-color: #1177bb;
      }
    }
  }

  .panel-content {
    height: 100%;
    padding: 16px;
    overflow-y: auto;
    background: #1e1e1e;

    .breadcrumb-container {
      margin-bottom: 16px;

      .ant-breadcrumb {
        color: #cccccc;

        .ant-breadcrumb-link {
          color: #cccccc;

          &:hover {
            color: #007acc;
          }
        }

        .ant-breadcrumb-separator {
          color: #858585;
        }
      }
    }

    .asset-grid-container {
      .asset-card {
        background: #252526;
        border-color: #3e3e42;

        &:hover {
          border-color: #007acc;
        }

        .asset-thumbnail {
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #1e1e1e;
        }

        .ant-card-meta-title {
          color: #cccccc;
          font-size: 12px;
        }

        .ant-card-meta-description {
          color: #858585;
          font-size: 11px;
        }
      }
    }

    .ant-empty {
      .ant-empty-description {
        color: #858585;
      }
    }
  }
}

